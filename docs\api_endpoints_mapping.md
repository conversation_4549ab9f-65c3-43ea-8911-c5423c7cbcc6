# MonIt API端点映射表

> **📅 生成时间**：2025年8月10日  
> **🔍 基于**：http://localhost:8000/docs 和代码分析  
> **📊 状态**：完整映射所有API端点到具体代码文件位置  

## 📋 API端点总览

### 🏠 根路径和系统状态
| HTTP方法 | 端点路径 | 函数名 | 文件位置 | 描述 |
|---------|---------|--------|----------|------|
| GET | `/` | `root()` | `backend/app/main.py:162` | 系统欢迎信息 |
| GET | `/system/status` | `get_system_status()` | `backend/app/main.py:171` | 获取系统状态 |
| GET | `/tasks` | `get_tasks()` | `backend/app/main.py:186` | 基础任务列表（备用） |

### 📊 任务管理 API (`/api/v1/tasks`)
**文件位置**: `backend/app/api/task_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/tasks/submit` | `submit_task()` | 153 | 提交爬取任务 |
| POST | `/api/v1/tasks/submit-single` | `submit_single_url()` | 210 | 提交单URL任务 |
| GET | `/api/v1/tasks/list` | `get_task_list()` | ~350 | 获取任务列表 |
| GET | `/api/v1/tasks/metrics` | `get_task_metrics()` | 371 | 获取任务指标 |

### 🎯 监控任务 API (`/api/v1/monitoring-tasks`)
**文件位置**: `backend/app/api/monitoring_task_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| GET | `/api/v1/monitoring-tasks/` | `get_monitoring_tasks()` | 294 | 获取监控任务列表 |
| POST | `/api/v1/monitoring-tasks/` | `create_monitoring_task()` | ~500 | 创建监控任务 |
| GET | `/api/v1/monitoring-tasks/{task_id}` | `get_monitoring_task()` | ~800 | 获取监控任务详情 |
| PUT | `/api/v1/monitoring-tasks/{task_id}` | `update_monitoring_task()` | ~1200 | 更新监控任务 |
| DELETE | `/api/v1/monitoring-tasks/{task_id}` | `delete_monitoring_task()` | ~1600 | 删除监控任务 |
| POST | `/api/v1/monitoring-tasks/{task_id}/start` | `start_monitoring_task()` | ~1800 | 启动监控任务 |
| POST | `/api/v1/monitoring-tasks/{task_id}/pause` | `pause_monitoring_task()` | ~1900 | 暂停监控任务 |
| POST | `/api/v1/monitoring-tasks/{task_id}/execute` | `execute_monitoring_task()` | 2064 | 执行监控任务 |
| GET | `/api/v1/monitoring-tasks/{task_id}/urls` | `get_task_urls()` | ~2100 | 获取任务URL列表 |
| PUT | `/api/v1/monitoring-tasks/{task_id}/urls` | `update_task_urls()` | ~2200 | 更新任务URL |

### 📈 监控指标 API (`/api/v1/monitoring`)
**文件位置**: `backend/app/api/monitoring_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| GET | `/api/v1/monitoring/metrics` | `get_system_metrics()` | 258 | 获取系统指标 |
| GET | `/api/v1/monitoring/alerts` | `get_active_alerts()` | 319 | 获取活跃告警 |
| GET | `/api/v1/monitoring/crawler-request-metrics/summary` | `get_crawler_request_metrics_summary()` | 481 | 爬虫请求指标摘要 |
| GET | `/api/v1/monitoring/crawler-request-metrics/task/{task_id}` | `get_task_crawler_metrics()` | 待查 | 任务爬虫指标 |
| GET | `/api/v1/monitoring/crawler-request-metrics/report/{task_id}` | `get_task_monitoring_report()` | 557 | 任务监控报告 |
| GET | `/api/v1/monitoring/crawler-request-metrics/performance-analysis` | `get_performance_analysis()` | 待查 | 性能分析报告 |
| GET | `/api/v1/monitoring/system-health` | `get_system_health()` | 待查 | 系统健康状态 |

### 📄 Excel处理 API (`/api/v1/excel`)
**文件位置**: `backend/app/api/excel_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/excel/parse` | `parse_excel_file()` | 64 | 解析Excel文件 |
| POST | `/api/v1/excel/validate` | `validate_urls()` | 160 | 验证URL列表 |

### 🌐 URL池管理 API (`/api/v1/url-pool`)
**文件位置**: `backend/app/api/url_pool_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/url-pool/excel/upload-and-parse` | `upload_and_parse_excel()` | 51 | 上传Excel文件并解析URL |
| GET | `/api/v1/url-pool/urls/pool` | `get_url_pool()` | 146 | 获取URL池列表 |
| POST | `/api/v1/url-pool/urls/pool/batch-update` | `batch_update_urls()` | 212 | 批量更新URL状态 |

### 🔧 任务创建 API (`/api/v1/task-create`)
**文件位置**: `backend/app/api/task_create_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/task-create/preview-urls` | `preview_selected_urls()` | 44 | 预览选中的URL信息 |
| POST | `/api/v1/task-create/validate` | `validate_task_creation()` | 67 | 验证任务创建请求 |
| POST | `/api/v1/task-create/create-from-urls` | `create_task_from_urls()` | 94 | 从URL池创建监控任务 |

### 🔄 Celery监控 API (`/api/v1/celery`)
**文件位置**: `backend/app/api/celery_monitoring_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| GET | `/api/v1/celery/workers` | `get_workers()` | ~100 | 获取Worker状态 |
| GET | `/api/v1/celery/tasks` | `get_active_tasks()` | ~200 | 获取活跃任务 |
| GET | `/api/v1/celery/tasks/live-stream` | `get_live_task_stream()` | ~300 | 获取实时任务流 |
| GET | `/api/v1/celery/stats` | `get_celery_stats()` | ~400 | 获取Celery统计 |

### 🕷️ 爬虫调试 API (`/api/v1/crawler-debug`)
**文件位置**: `backend/app/api/crawler_debug_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| GET | `/api/v1/crawler-debug/task-relations/by-celery-task/{celery_task_id}` | `get_task_relation_by_celery_id()` | 379 | 通过Celery任务ID获取任务关联信息 |
| GET | `/api/v1/crawler-debug/task-relations/by-monitoring-task/{monitoring_task_id}` | `get_monitoring_task_relations()` | ~420 | 获取监控任务的关联信息 |
| GET | `/api/v1/crawler-debug/task-relations/by-worker/{worker_id}` | `get_worker_tasks()` | 432 | 通过Worker ID获取关联的任务 |
| GET | `/api/v1/crawler-debug/task-relations/by-url` | `get_url_tasks()` | 445 | 通过URL获取关联的任务 |

## 🆕 新架构 API (v1)

### 🔧 爬虫配置管理 (`/api/v1/crawler`)
**文件位置**: `backend/app/api/v1/crawler_config.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| GET | `/api/v1/crawler/configs/` | `list_crawler_configs()` | 298 | 获取爬虫配置列表 |
| GET | `/api/v1/crawler/templates/` | `get_config_templates()` | 522 | 获取配置模板 |
| GET | `/api/v1/crawler/config` | `get_crawler_config()` | 1111 | 获取爬虫配置 |
| GET | `/api/v1/crawler/config/default` | `get_default_crawler_config()` | 1124 | 获取默认爬虫配置 |

### 🚀 爬虫执行管理 (`/api/v1/crawler`)
**文件位置**: `backend/app/api/v1/crawler_execution.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/crawler/execute` | `execute_crawler_task()` | 46 | 执行爬虫任务 |
| POST | `/api/v1/crawler/execute/batch` | `batch_execute_crawler_tasks()` | ~150 | 批量执行爬虫任务 |
| POST | `/api/v1/crawler/test` | `test_crawler_api_connection()` | 218 | 测试爬虫API连接 |

### 🔧 爬虫配置管理 (`/api/v1/crawler-configs`)
**文件位置**: `backend/app/api/v1/crawler_config_new.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/crawler-configs/` | `create_crawler_config()` | 24 | 创建爬虫配置 |
| GET | `/api/v1/crawler-configs/` | `list_crawler_configs()` | ~50 | 获取配置列表 |
| GET | `/api/v1/crawler-configs/{config_id}` | `get_crawler_config()` | ~100 | 获取配置详情 |
| PUT | `/api/v1/crawler-configs/{config_id}` | `update_crawler_config()` | ~150 | 更新配置 |
| DELETE | `/api/v1/crawler-configs/{config_id}` | `delete_crawler_config()` | ~200 | 删除配置 |

### 🖥️ 后端配置管理 (`/api/v1/backend-configs`)
**文件位置**: `backend/app/api/v1/backend_config.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/backend-configs/` | `create_backend_config()` | 23 | 创建后端配置 |
| GET | `/api/v1/backend-configs/` | `list_backend_configs()` | 待查 | 获取配置列表 |
| GET | `/api/v1/backend-configs/{config_id}` | `get_backend_config()` | 待查 | 获取配置详情 |

### 🏭 爬虫实例配置管理 (`/api/v1/crawler/instances`)
**文件位置**: `backend/app/api/v1/crawler_instance_config.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| GET | `/api/v1/crawler/instances/` | `get_all_instance_configs()` | 20 | 获取所有爬虫实例配置 |

### 👷 爬虫Worker管理 (`/api/v1/crawler-workers`)
**文件位置**: `backend/app/api/v1/crawler_worker.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/crawler-workers/` | `create_crawler_worker()` | 24 | 创建Worker |
| GET | `/api/v1/crawler-workers/` | `list_crawler_workers()` | ~80 | 获取Worker列表 |
| GET | `/api/v1/crawler-workers/{worker_id}` | `get_crawler_worker()` | ~120 | 获取Worker详情 |
| PUT | `/api/v1/crawler-workers/{worker_id}` | `update_crawler_worker()` | ~160 | 更新Worker |
| DELETE | `/api/v1/crawler-workers/{worker_id}` | `delete_crawler_worker()` | ~200 | 删除Worker |
| POST | `/api/v1/crawler-workers/{worker_id}/record-execution` | `record_task_execution()` | 304 | 记录任务执行 |

### 📋 任务分配管理 (`/api/v1/task-assignments`)
**文件位置**: `backend/app/api/v1/task_assignment.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| POST | `/api/v1/task-assignments/` | `create_task_assignment()` | 22 | 创建任务分配 |
| GET | `/api/v1/task-assignments/` | `list_task_assignments()` | ~80 | 获取分配列表 |
| GET | `/api/v1/task-assignments/{assignment_id}` | `get_task_assignment()` | ~120 | 获取分配详情 |
| PUT | `/api/v1/task-assignments/{assignment_id}` | `update_task_assignment()` | ~160 | 更新分配 |
| DELETE | `/api/v1/task-assignments/{assignment_id}` | `delete_task_assignment()` | ~200 | 删除分配 |
| POST | `/api/v1/task-assignments/{assignment_id}/start` | `start_task_assignment()` | 249 | 启动任务分配 |
| POST | `/api/v1/task-assignments/{assignment_id}/complete` | `complete_task_assignment()` | 274 | 完成任务分配 |

## 🔧 爬取配置管理 API (`/api/v1/crawl-configs`)
**文件位置**: `backend/app/api/crawl_config_routes.py`

| HTTP方法 | 端点路径 | 函数名 | 行号 | 描述 |
|---------|---------|--------|------|------|
| GET | `/api/v1/crawl-configs/debug` | `debug_redis()` | 317 | 调试Redis连接 |
| GET | `/api/v1/crawl-configs/` | `list_crawl_configs()` | 348 | 获取爬取配置列表 |
| POST | `/api/v1/crawl-configs/test` | `test_endpoint()` | 423 | 测试端点 |
| GET | `/api/v1/crawl-configs/platforms/` | `get_platforms()` | 733 | 获取支持的平台列表 |
| GET | `/api/v1/crawl-configs/templates/` | `get_templates()` | 769 | 获取配置模板列表 |
| POST | `/api/v1/crawl-configs/execute` | `execute_task()` | 812 | 执行爬虫任务 |
| GET | `/api/v1/crawl-configs/tasks/{task_id}/executions` | `get_task_executions()` | 922 | 获取任务执行历史 |

## 📝 注意事项

1. **待查项目**: 标记为"待查"的行号需要进一步检查具体代码文件确定
2. **路由前缀**: 所有API都通过 `main.py` 中的路由注册进行统一管理
3. **版本控制**: 新架构API使用 `/api/v1` 前缀，旧API可能没有版本前缀
4. **文档同步**: 建议定期更新此映射表以保持与实际代码的同步

## 🔄 下一步行动

1. 完善"待查"的具体行号信息
2. 验证所有API端点的实际可用性
3. 添加请求/响应模型的详细信息
4. 创建API测试用例覆盖所有端点
