"""
重新设计的爬虫配置管理API
基于外部爬虫API的配置管理
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
import json
import uuid
from datetime import datetime
from enum import Enum
from pathlib import Path

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from config.settings import Settings
import redis
from app.services.crawler_api_service import TaskPriority, get_crawler_api_service

settings = Settings()

def get_redis_client():
    """获取Redis客户端"""
    return redis.from_url(settings.REDIS_URL, decode_responses=True)

router = APIRouter()


class CrawlerConfigType(str, Enum):
    """爬虫配置类型"""
    BATCH_TASK = "batch_task"  # 批量任务配置
    SINGLE_TASK = "single_task"  # 单个任务配置
    TEMPLATE = "template"  # 配置模板


class PlatformType(str, Enum):
    """平台类型"""
    MERCADOLIBRE = "mercadolibre"
    AMAZON = "amazon"
    ALIBABA_1688 = "1688"
    GENERAL = "general"


class CrawlerTaskConfig(BaseModel):
    """爬虫任务配置"""
    id: Optional[str] = Field(None, description="配置ID")
    name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    description: Optional[str] = Field(None, max_length=500, description="配置描述")
    config_type: CrawlerConfigType = Field(CrawlerConfigType.BATCH_TASK, description="配置类型")
    platform: PlatformType = Field(PlatformType.GENERAL, description="目标平台")

    # LLM查询配置
    llm_query: str = Field(..., min_length=1, description="LLM查询指令")
    llm_schema: Optional[str] = Field(None, description="LLM输出模式")

    # 任务配置
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="默认任务优先级")
    cache_enabled: bool = Field(False, description="是否启用缓存")
    max_concurrent_tasks: Optional[int] = Field(None, ge=1, le=50, description="最大并发任务数")
    batch_timeout: Optional[int] = Field(None, ge=60, le=86400, description="批次超时时间(秒)")

    # 回调配置
    callback_url: Optional[str] = Field(None, description="回调URL")

    # 元数据
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    created_by: Optional[str] = Field(None, description="创建者")
    is_active: bool = Field(True, description="是否激活")

    # 使用统计
    usage_count: int = Field(0, description="使用次数")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")

    def __init__(self, **data):
        if 'id' not in data or not data['id']:
            data['id'] = str(uuid.uuid4())
        if 'created_at' not in data:
            data['created_at'] = datetime.now()
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        super().__init__(**data)


class CrawlerConfigTemplate(BaseModel):
    """爬虫配置模板"""
    id: Optional[str] = Field(None, description="模板ID")
    name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, max_length=500, description="模板描述")
    platform: PlatformType = Field(..., description="目标平台")

    # 模板配置
    template_config: Dict[str, Any] = Field(..., description="模板配置数据")

    # 预设查询
    preset_queries: List[str] = Field(default_factory=list, description="预设查询列表")

    # 元数据
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    is_builtin: bool = Field(False, description="是否内置模板")
    usage_count: int = Field(0, description="使用次数")

    def __init__(self, **data):
        if 'id' not in data or not data['id']:
            data['id'] = str(uuid.uuid4())
        if 'created_at' not in data:
            data['created_at'] = datetime.now()
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        super().__init__(**data)


class CrawlerConfigCreate(BaseModel):
    """创建爬虫配置请求"""
    name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    description: Optional[str] = Field(None, max_length=500, description="配置描述")
    config_type: CrawlerConfigType = Field(CrawlerConfigType.BATCH_TASK, description="配置类型")
    platform: PlatformType = Field(PlatformType.GENERAL, description="目标平台")
    llm_query: str = Field(..., min_length=1, description="LLM查询指令")
    llm_schema: Optional[str] = Field(None, description="LLM输出模式")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="默认任务优先级")
    cache_enabled: bool = Field(False, description="是否启用缓存")
    max_concurrent_tasks: Optional[int] = Field(None, ge=1, le=50, description="最大并发任务数")
    batch_timeout: Optional[int] = Field(None, ge=60, le=86400, description="批次超时时间(秒)")
    callback_url: Optional[str] = Field(None, description="回调URL")


class CrawlerConfigUpdate(BaseModel):
    """更新爬虫配置请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="配置名称")
    description: Optional[str] = Field(None, max_length=500, description="配置描述")
    platform: Optional[PlatformType] = Field(None, description="目标平台")
    llm_query: Optional[str] = Field(None, min_length=1, description="LLM查询指令")
    llm_schema: Optional[str] = Field(None, description="LLM输出模式")
    priority: Optional[TaskPriority] = Field(None, description="默认任务优先级")
    cache_enabled: Optional[bool] = Field(None, description="是否启用缓存")
    max_concurrent_tasks: Optional[int] = Field(None, ge=1, le=50, description="最大并发任务数")
    batch_timeout: Optional[int] = Field(None, ge=60, le=86400, description="批次超时时间(秒)")
    callback_url: Optional[str] = Field(None, description="回调URL")
    is_active: Optional[bool] = Field(None, description="是否激活")


class CrawlerConfigService:
    """爬虫配置服务"""

    def __init__(self):
        self.redis_client = get_redis_client()
        self.config_key_prefix = "crawler_configs"
        self.template_key_prefix = "crawler_templates"

    async def create_config(self, config_data: CrawlerConfigCreate, created_by: str = "system") -> CrawlerTaskConfig:
        """创建爬虫配置"""
        config = CrawlerTaskConfig(
            **config_data.dict(),
            created_by=created_by
        )

        # 存储到Redis
        config_key = f"{self.config_key_prefix}:{config.id}"
        config_dict = config.dict()

        # 转换datetime为字符串
        for key, value in config_dict.items():
            if isinstance(value, datetime):
                config_dict[key] = value.isoformat()

        self.redis_client.hset(config_key, mapping=config_dict)

        # 添加到索引
        self.redis_client.sadd(f"{self.config_key_prefix}:index", config.id)
        self.redis_client.sadd(f"{self.config_key_prefix}:by_platform:{config.platform.value}", config.id)
        self.redis_client.sadd(f"{self.config_key_prefix}:by_type:{config.config_type.value}", config.id)

        return config

    async def get_config(self, config_id: str) -> Optional[CrawlerTaskConfig]:
        """获取爬虫配置"""
        config_key = f"{self.config_key_prefix}:{config_id}"
        config_data = self.redis_client.hgetall(config_key)

        if not config_data:
            return None

        # 转换字符串为datetime
        for key in ['created_at', 'updated_at', 'last_used_at']:
            if config_data.get(key):
                config_data[key] = datetime.fromisoformat(config_data[key])

        # 转换数值类型
        for key in ['usage_count', 'max_concurrent_tasks', 'batch_timeout']:
            if config_data.get(key):
                config_data[key] = int(config_data[key])

        # 转换布尔类型
        for key in ['cache_enabled', 'is_active']:
            if config_data.get(key):
                config_data[key] = config_data[key].lower() == 'true'

        return CrawlerTaskConfig(**config_data)

    async def update_config(self, config_id: str, update_data: CrawlerConfigUpdate) -> Optional[CrawlerTaskConfig]:
        """更新爬虫配置"""
        config = await self.get_config(config_id)
        if not config:
            return None

        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            setattr(config, key, value)

        config.updated_at = datetime.now()

        # 保存到Redis
        config_key = f"{self.config_key_prefix}:{config_id}"
        config_dict = config.dict()

        # 转换datetime为字符串
        for key, value in config_dict.items():
            if isinstance(value, datetime):
                config_dict[key] = value.isoformat()

        self.redis_client.hset(config_key, mapping=config_dict)

        return config

    async def delete_config(self, config_id: str) -> bool:
        """删除爬虫配置"""
        config = await self.get_config(config_id)
        if not config:
            return False

        # 从Redis删除
        config_key = f"{self.config_key_prefix}:{config_id}"
        self.redis_client.delete(config_key)

        # 从索引删除
        self.redis_client.srem(f"{self.config_key_prefix}:index", config_id)
        self.redis_client.srem(f"{self.config_key_prefix}:by_platform:{config.platform.value}", config_id)
        self.redis_client.srem(f"{self.config_key_prefix}:by_type:{config.config_type.value}", config_id)

        return True

    async def list_configs(
        self,
        platform: Optional[PlatformType] = None,
        config_type: Optional[CrawlerConfigType] = None,
        is_active: Optional[bool] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """列出爬虫配置"""

        # 构建查询条件
        if platform:
            config_ids = self.redis_client.smembers(f"{self.config_key_prefix}:by_platform:{platform.value}")
        elif config_type:
            config_ids = self.redis_client.smembers(f"{self.config_key_prefix}:by_type:{config_type.value}")
        else:
            config_ids = self.redis_client.smembers(f"{self.config_key_prefix}:index")

        # 获取配置详情
        configs = []
        for config_id in config_ids:
            config = await self.get_config(config_id)
            if config:
                if is_active is not None and config.is_active != is_active:
                    continue
                configs.append(config)

        # 排序
        configs.sort(key=lambda x: x.updated_at, reverse=True)

        # 分页
        total = len(configs)
        start = (page - 1) * page_size
        end = start + page_size
        configs = configs[start:end]

        return {
            "configs": configs,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }


# 全局配置服务实例
config_service = CrawlerConfigService()


# API路由
@router.get("/configs/", response_model=Dict[str, Any])
async def list_crawler_configs(
    platform: Optional[PlatformType] = None,
    config_type: Optional[CrawlerConfigType] = None,
    is_active: Optional[bool] = None,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小")
):
    """获取爬虫配置列表"""
    try:
        result = await config_service.list_configs(
            platform=platform,
            config_type=config_type,
            is_active=is_active,
            page=page,
            page_size=page_size
        )
        return {
            "success": True,
            "data": result,
            "message": "获取配置列表成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置列表失败: {str(e)}")


@router.post("/configs/", response_model=Dict[str, Any])
async def create_crawler_config(config_data: CrawlerConfigCreate):
    """创建爬虫配置"""
    try:
        config = await config_service.create_config(config_data)
        return {
            "success": True,
            "data": config.dict(),
            "message": "创建配置成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")


@router.get("/configs/{config_id}", response_model=Dict[str, Any])
async def get_crawler_config(config_id: str):
    """获取单个爬虫配置"""
    try:
        config = await config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        return {
            "success": True,
            "data": config.dict(),
            "message": "获取配置成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.put("/configs/{config_id}", response_model=Dict[str, Any])
async def update_crawler_config(config_id: str, update_data: CrawlerConfigUpdate):
    """更新爬虫配置"""
    try:
        config = await config_service.update_config(config_id, update_data)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        return {
            "success": True,
            "data": config.dict(),
            "message": "更新配置成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.delete("/configs/{config_id}", response_model=Dict[str, Any])
async def delete_crawler_config(config_id: str):
    """删除爬虫配置"""
    try:
        success = await config_service.delete_config(config_id)
        if not success:
            raise HTTPException(status_code=404, detail="配置不存在")

        return {
            "success": True,
            "data": None,
            "message": "删除配置成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")


@router.post("/configs/{config_id}/validate", response_model=Dict[str, Any])
async def validate_crawler_config(config_id: str):
    """验证爬虫配置"""
    try:
        config = await config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        # 获取爬虫API服务
        crawler_api = await get_crawler_api_service()

        # 检查外部API连接
        try:
            system_status = await crawler_api.get_system_status()
            api_available = True
            api_status = system_status.get("status", "unknown")
        except Exception as e:
            api_available = False
            api_status = f"连接失败: {str(e)}"

        # 验证配置完整性
        validation_results = {
            "config_valid": True,
            "api_available": api_available,
            "api_status": api_status,
            "validation_details": {
                "llm_query": bool(config.llm_query and len(config.llm_query.strip()) > 0),
                "platform": bool(config.platform),
                "priority": bool(config.priority),
                "timeout_valid": config.batch_timeout is None or (60 <= config.batch_timeout <= 86400),
                "concurrent_valid": config.max_concurrent_tasks is None or (1 <= config.max_concurrent_tasks <= 50)
            }
        }

        # 检查是否所有验证都通过
        all_valid = (
            validation_results["api_available"] and
            all(validation_results["validation_details"].values())
        )

        validation_results["config_valid"] = all_valid

        return {
            "success": True,
            "data": validation_results,
            "message": "配置验证完成"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置验证失败: {str(e)}")


class TestConfigRequest(BaseModel):
    """测试配置请求"""
    test_url: str = Field(..., description="测试URL")

@router.post("/configs/{config_id}/test", response_model=Dict[str, Any])
async def test_crawler_config(
    config_id: str,
    request: TestConfigRequest
):
    """测试爬虫配置"""
    try:
        config = await config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        # 获取爬虫API服务
        crawler_api = await get_crawler_api_service()

        # 创建测试任务
        from app.services.crawler_api_service import LlmJobRequest

        test_request = LlmJobRequest(
            url=request.test_url,
            q=config.llm_query,
            llm_schema=config.llm_schema
        )

        # 提交测试任务
        result = await crawler_api.submit_llm_job(test_request)

        # 更新配置使用统计
        config.usage_count += 1
        config.last_used_at = datetime.now()
        await config_service.update_config(config_id, CrawlerConfigUpdate(
            usage_count=config.usage_count,
            last_used_at=config.last_used_at
        ))

        return {
            "success": True,
            "data": {
                "test_result": result,
                "config_id": config_id,
                "test_url": test_url
            },
            "message": "配置测试成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置测试失败: {str(e)}")


@router.get("/platforms/", response_model=Dict[str, Any])
async def get_supported_platforms():
    """获取支持的平台列表"""
    platforms = [
        {
            "value": platform.value,
            "label": platform.value.upper(),
            "description": f"{platform.value} 平台配置"
        }
        for platform in PlatformType
    ]

    return {
        "success": True,
        "data": platforms,
        "message": "获取平台列表成功"
    }


@router.get("/templates/", response_model=Dict[str, Any])
async def get_config_templates():
    """获取配置模板"""
    # 内置模板
    builtin_templates = [
        {
            "id": "mercadolibre_product",
            "name": "MercadoLibre商品信息",
            "platform": "mercadolibre",
            "llm_query": "提取商品名称、价格、评分、库存状态、主图URL和商品描述",
            "llm_schema": '{"name": "string", "price": "number", "rating": "number", "stock": "string", "image_url": "string", "description": "string"}',
            "is_builtin": True
        },
        {
            "id": "amazon_product",
            "name": "Amazon商品信息",
            "platform": "amazon",
            "llm_query": "提取商品标题、价格、评分、评论数量、可用性状态和产品特性",
            "llm_schema": '{"title": "string", "price": "number", "rating": "number", "reviews_count": "number", "availability": "string", "features": "array"}',
            "is_builtin": True
        },
        {
            "id": "1688_product",
            "name": "1688商品信息",
            "platform": "1688",
            "llm_query": "提取商品名称、价格区间、最小起订量、供应商信息和商品规格",
            "llm_schema": '{"name": "string", "price_range": "string", "min_order": "number", "supplier": "string", "specifications": "object"}',
            "is_builtin": True
        }
    ]

    return {
        "success": True,
        "data": builtin_templates,
        "message": "获取模板列表成功"
    }
    """内容处理配置"""
    word_count_threshold: int = Field(200, ge=0, le=10000)
    css_selector: str = ""
    target_elements: List[str] = []
    excluded_tags: List[str] = ["nav", "footer", "aside"]
    excluded_selector: str = ""
    remove_forms: bool = False
    only_text: bool = False
    prettify: bool = False
    parser_type: str = "lxml"
    keep_data_attributes: bool = False
    keep_attrs: List[str] = []

class LinkFilteringConfig(BaseModel):
    """链接过滤配置"""
    exclude_external_links: bool = False
    exclude_internal_links: bool = False
    exclude_social_media_links: bool = False
    exclude_domains: List[str] = ["example.com", "ads.google.com", "facebook.com", "twitter.com"]
    social_media_domains: List[str] = [
        "facebook.com", "twitter.com", "instagram.com", "linkedin.com",
        "youtube.com", "tiktok.com", "pinterest.com", "reddit.com"
    ]
    exclude_external_images: bool = False
    exclude_all_images: bool = False
    image_score_threshold: int = Field(3, ge=0, le=100)
    image_description_min_word_threshold: int = Field(50, ge=0, le=200)
    table_score_threshold: int = Field(7, ge=0, le=20)

class SchedulerConfig(BaseModel):
    """调度器配置"""
    semaphore_count: int = Field(2, ge=1, le=500)
    mean_delay: float = Field(0.1, ge=0, le=10)
    max_range: int = Field(3, ge=1, le=10)
    pool_size: int = Field(10, ge=5, le=50)
    memory_threshold: int = Field(4096, ge=1024, le=16384)

class MonitorConfig(BaseModel):
    """监控配置"""
    display_mode: str = Field("detailed", pattern="^(simple|detailed|debug)$")
    show_progress: bool = True
    log_errors: bool = True

class APIConfig(BaseModel):
    """API配置"""
    base_url: str = "http://localhost:11234"
    timeout: int = Field(30000, ge=1000, le=300000)
    max_retries: int = Field(3, ge=0, le=10)

class BrowserConfig(BaseModel):
    """浏览器配置"""
    headless: bool = True
    user_agent: Optional[str] = None
    viewport_width: int = Field(1920, ge=800, le=3840)
    viewport_height: int = Field(1080, ge=600, le=2160)
    timeout: int = Field(30000, ge=5000, le=120000)

class CrawlerConfig(BaseModel):
    """爬虫配置"""
    max_pages: int = Field(100, ge=1, le=10000)
    delay_between_requests: float = Field(1.0, ge=0.1, le=10.0)
    follow_redirects: bool = True
    respect_robots_txt: bool = False

class LLMConfig(BaseModel):
    """LLM配置"""
    model: str = Field("gpt-3.5-turbo", description="LLM模型名称")
    api_key: Optional[str] = Field(None, description="API密钥")
    temperature: float = Field(0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(1000, ge=100, le=4000)

class SchemaExtractionConfig(BaseModel):
    """模式提取配置"""
    enabled: bool = True
    strict_mode: bool = False
    fallback_to_text: bool = True

class ContentProcessingConfig(BaseModel):
    """内容处理配置"""
    remove_scripts: bool = True
    remove_styles: bool = True
    clean_html: bool = True
    extract_text_only: bool = False

class CrawlerFullConfig(BaseModel):
    """完整的爬虫配置"""
    api: APIConfig
    browser: BrowserConfig
    crawler: CrawlerConfig
    llm: LLMConfig
    schema_extraction: SchemaExtractionConfig
    content_processing: ContentProcessingConfig
    link_filtering: LinkFilteringConfig
    scheduler: SchedulerConfig
    monitor: MonitorConfig

    @validator('llm')
    def validate_llm_config(cls, v):
        """验证LLM配置"""
        # 允许API密钥为空，在实际使用时再验证
        if v.api_key and len(v.api_key.strip()) > 0 and len(v.api_key.strip()) < 10:
            raise ValueError("API密钥长度至少10个字符")
        return v

class CrawlerConfigService:
    """爬虫配置服务"""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.config_key = "crawler:config"
        # 创建数据目录
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        self.config_file = data_dir / "crawler_config.json"
        
    async def get_config(self) -> CrawlerFullConfig:
        """获取爬虫配置"""
        try:
            # 优先从Redis获取
            config_data = self.redis_client.get(self.config_key)
            if config_data:
                config_dict = json.loads(config_data)
                return CrawlerFullConfig(**config_dict)

            # 从文件获取
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    config = CrawlerFullConfig(**config_dict)
                    # 同步到Redis
                    await self.save_config(config)
                    return config

            # 返回默认配置
            return self._get_default_config()

        except Exception as e:
            print(f"获取配置失败: {e}")
            return self._get_default_config()
    
    async def save_config(self, config: CrawlerFullConfig) -> bool:
        """保存爬虫配置"""
        try:
            config_dict = config.dict()

            # 保存到Redis
            self.redis_client.set(
                self.config_key,
                json.dumps(config_dict, ensure_ascii=False),
                ex=86400 * 30  # 30天过期
            )

            # 保存到文件
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def _get_default_config(self) -> CrawlerFullConfig:
        """获取默认配置"""
        # 默认的电商商品信息提取Schema
        default_extraction_schema = {
            "type": "object",
            "description": "电商商品完整信息提取结构",
            "properties": {
                "task_info": {
                    "type": "object",
                    "description": "任务执行信息",
                    "properties": {
                        "start_timestamp": {
                            "type": "string",
                            "pattern": "^[0-9]{14}$",
                            "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
                        }
                    },
                    "required": ["start_timestamp"]
                },
                "product_basic_info": {
                    "type": "object",
                    "description": "商品基础信息",
                    "properties": {
                        "product_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品链接"
                        },
                        "product_name": {
                            "type": "string",
                            "description": "商品名称"
                        },
                        "mlm_id": {
                            "type": "number",
                            "description": "商品MLM-ID中的数值部分"
                        }
                    },
                    "required": ["product_url", "product_name", "mlm_id"]
                },
                "pricing_info": {
                    "type": "object",
                    "description": "价格和库存信息",
                    "properties": {
                        "sales_count": {
                            "type": "number",
                            "description": "商品销量（格式：+xxx vendidos中的数值）"
                        },
                        "current_price": {
                            "type": "number",
                            "description": "商品现价（数值）"
                        },
                        "original_price": {
                            "type": "number",
                            "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
                        },
                        "discount_rate": {
                            "type": "number",
                            "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
                        },
                        "stock_quantity": {
                            "type": "number",
                            "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
                        },
                        "stocktype_IsFull": {
                            "type": "number",
                            "description": "商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\":\"Full\"，商品有就是1，没有就是0"
                        }
                    },
                    "required": ["current_price"]
                },
                "category_info": {
                    "type": "object",
                    "description": "商品目录分类信息",
                    "properties": {
                        "category_breadcrumb": {
                            "type": "string",
                            "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"
                        },
                        "category_hierarchy": {
                            "type": "array",
                            "description": "商品各级目录详细信息",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "category_name": {
                                        "type": "string",
                                        "description": "目录名称"
                                    },
                                    "category_url": {
                                        "type": "string",
                                        "format": "uri",
                                        "description": "目录链接"
                                    },
                                    "level": {
                                        "type": "integer",
                                        "description": "目录层级（1为顶级，2为二级，以此类推）"
                                    }
                                },
                                "required": ["category_name", "level"]
                            }
                        }
                    },
                    "required": ["category_breadcrumb"]
                },
                "seller_info": {
                    "type": "object",
                    "description": "销售商信息",
                    "properties": {
                        "seller_name": {
                            "type": "string",
                            "description": "商品销售商名称"
                        },
                        "seller_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品销售商链接"
                        }
                    },
                    "required": ["seller_name"]
                },
                "media_info": {
                    "type": "object",
                    "description": "商品媒体信息",
                    "properties": {
                        "main_image_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）"
                        }
                    },
                    "required": ["main_image_url"]
                },
                "qa_section": {
                    "type": "object",
                    "description": "问答区域信息",
                    "properties": {
                        "questions": {
                            "type": "array",
                            "description": "商品页面的问题列表",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "question_content": {
                                        "type": "string",
                                        "description": "问题内容"
                                    },
                                    "question_time": {
                                        "type": "string",
                                        "description": "问题时间"
                                    },
                                    "answer_content": {
                                        "type": "string",
                                        "description": "问题回答"
                                    }
                                },
                                "required": ["question_content", "answer_content"]
                            }
                        },
                        "all_questions_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接"
                        }
                    }
                },
                "rating_info": {
                    "type": "object",
                    "description": "评分信息",
                    "properties": {
                        "rating_score": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 5,
                            "description": "商品评分数（0-5分）"
                        },
                        "rating_count": {
                            "type": "integer",
                            "description": "商品评分数量"
                        }
                    },
                    "required": ["rating_score", "rating_count"]
                },
                "reviews_section": {
                    "type": "object",
                    "description": "评论区域信息",
                    "properties": {
                        "reviews": {
                            "type": "array",
                            "description": "商品评论列表",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "review_content": {
                                        "type": "string",
                                        "description": "评论内容"
                                    },
                                    "review_rating": {
                                        "type": "number",
                                        "minimum": 0,
                                        "maximum": 5,
                                        "description": "评论评分（0-5分）"
                                    },
                                    "review_time": {
                                        "type": "string",
                                        "description": "评论时间"
                                    }
                                },
                                "required": ["review_content", "review_time"]
                            }
                        },
                        "all_reviews_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接"
                        }
                    }
                }
            },
            "required": [
                "product_basic_info",
                "category_info",
                "pricing_info",
                "media_info",
                "rating_info"
            ]
        }

        default_instructions = """请严格按照以下条件提取商品信息：
1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品
2. 如果页面有多个商品，只提取最突出显示的主商品
3. 重点关注页面标题中提到的商品
4. 忽略广告推荐和次要商品信息
5. 确保提取的商品名称与页面URL或页面标题相匹配
6. 确保提取的商品mlmid与页面URL的mlmid相匹配

需要提取的信息如下：
商品链接、商品名称、商品MLM-ID；
商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\":\\"Full\\"，商品有就是1，没有就是0)；
获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；
商品销售商名称、商品销售商链接；
商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；
商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；
商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；
商品评分数、商品评分数量、
商品的评论内容、评论评分、评论时间；
商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；
任务开始的时间戳YYYYMMDDHHMMSS"""

        return CrawlerFullConfig(
            api=APIConfig(),
            browser=BrowserConfig(),
            crawler=CrawlerConfig(),
            llm=LLMConfig(
                api_key="sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi"  # 使用示例中的API密钥
            ),
            schema_extraction=SchemaExtractionConfig(
                enabled=True,
                schema_type="auto",
                extraction_schema=default_extraction_schema,
                instructions=default_instructions,
                validate_schema=True,
                return_raw=False
            ),
            content_processing=ContentProcessingConfig(),
            link_filtering=LinkFilteringConfig(),
            scheduler=SchedulerConfig(),
            monitor=MonitorConfig()
        )
    
    def build_crawl4ai_request(self, config: CrawlerFullConfig, urls: List[str]) -> Dict[str, Any]:
        """构建Crawl4AI API请求"""
        return {
            "urls": urls,
            "browser_config": {
                "headless": config.browser.headless,
                "verbose": config.browser.verbose,
                "viewport_width": config.browser.viewport_width,
                "viewport_height": config.browser.viewport_height,
                "wait_for": config.browser.wait_for,
                "timeout": config.browser.timeout,
                "ignore_https_errors": config.browser.ignore_https_errors,
                "extra_args": config.browser.extra_args
            },
            "crawler_config": {
                "method": config.crawler.method,
                "verbose": config.crawler.verbose,
                "check_robots_txt": config.crawler.check_robots_txt,
                "fetch_ssl_certificate": config.crawler.fetch_ssl_certificate,
                "simulate_user": config.crawler.simulate_user,
                "magic": config.crawler.magic,
                "override_navigator": config.crawler.override_navigator,
                "remove_overlay_elements": config.crawler.remove_overlay_elements,
                "ignore_body_visibility": config.crawler.ignore_body_visibility,
                "adjust_viewport_to_content": config.crawler.adjust_viewport_to_content,
                "wait_until": config.crawler.wait_until,
                "wait_for_images": config.crawler.wait_for_images,
                "page_timeout": config.crawler.page_timeout,
                "delay_before_return_html": config.crawler.delay_before_return_html,
                "js_only": config.crawler.js_only,
                "scan_full_page": config.crawler.scan_full_page,
                "process_iframes": config.crawler.process_iframes,
                "scroll_delay": config.crawler.scroll_delay,
                "cache_mode": config.crawler.cache_mode,
                "screenshot": config.crawler.screenshot,
                "pdf": config.crawler.pdf,
                "capture_mhtml": config.crawler.capture_mhtml,
                "exclude_external_images": config.crawler.exclude_external_images,
                "exclude_all_images": config.crawler.exclude_all_images,
                "image_score_threshold": config.crawler.image_score_threshold,
                "image_description_min_word_threshold": config.crawler.image_description_min_word_threshold,
                "table_score_threshold": config.crawler.table_score_threshold,
                "capture_network_requests": config.crawler.capture_network_requests,
                "capture_console_messages": config.crawler.capture_console_messages,
                "log_console": config.crawler.log_console,
                "extraction_strategy": config.crawler.extraction_strategy,
                "chunking_strategy": config.crawler.chunking_strategy,
                "markdown_generator": config.crawler.markdown_generator,
                "bypass_cache": config.crawler.bypass_cache,
                "llm_extraction": {
                    "query": config.llm.query,
                    "provider": config.llm.provider,
                    "model": config.llm.model,
                    "api_key": config.llm.api_key,
                    "base_url": config.llm.base_url,
                    "temperature": config.llm.temperature,
                    "max_tokens": config.llm.max_tokens,
                    "top_p": config.llm.top_p
                },
                "schema_extraction": {
                    "schema": config.schema_extraction.extraction_schema,
                    "instructions": config.schema_extraction.instructions,
                    "validate_schema": config.schema_extraction.validate_schema,
                    "return_raw": config.schema_extraction.return_raw
                },
                "content_processing": {
                    "word_count_threshold": config.content_processing.word_count_threshold,
                    "css_selector": config.content_processing.css_selector,
                    "target_elements": config.content_processing.target_elements,
                    "excluded_tags": config.content_processing.excluded_tags,
                    "excluded_selector": config.content_processing.excluded_selector,
                    "remove_forms": config.content_processing.remove_forms,
                    "only_text": config.content_processing.only_text,
                    "prettify": config.content_processing.prettify,
                    "parser_type": config.content_processing.parser_type,
                    "keep_data_attributes": config.content_processing.keep_data_attributes,
                    "keep_attrs": config.content_processing.keep_attrs
                },
                "link_filtering": {
                    "exclude_external_links": config.link_filtering.exclude_external_links,
                    "exclude_internal_links": config.link_filtering.exclude_internal_links,
                    "exclude_social_media_links": config.link_filtering.exclude_social_media_links,
                    "exclude_domains": config.link_filtering.exclude_domains,
                    "social_media_domains": config.link_filtering.social_media_domains,
                    "exclude_external_images": config.link_filtering.exclude_external_images,
                    "exclude_all_images": config.link_filtering.exclude_all_images,
                    "image_score_threshold": config.link_filtering.image_score_threshold,
                    "image_description_min_word_threshold": config.link_filtering.image_description_min_word_threshold,
                    "table_score_threshold": config.link_filtering.table_score_threshold
                },
                "_config_summary": {
                    "enabled_features": {
                        "llm_extraction": True,
                        "css_extraction": False,
                        "schema_extraction": True,
                        "content_processing": True,
                        "link_filtering": True
                    },
                    "strategy_used": config.crawler.extraction_strategy
                },
                "semaphore_count": config.scheduler.semaphore_count,
                "stream": True,
                "mean_delay": config.scheduler.mean_delay,
                "max_range": config.scheduler.max_range,
                "task_type": "batch",
                "scheduler": {
                    "type": "AsyncQueueManager",
                    "pool_size": config.scheduler.pool_size,
                    "memory_threshold": config.scheduler.memory_threshold
                },
                "monitor": {
                    "display_mode": config.monitor.display_mode,
                    "show_progress": config.monitor.show_progress,
                    "log_errors": config.monitor.log_errors
                }
            }
        }

# 全局配置服务实例
config_service = CrawlerConfigService()



@router.get("/config/default", response_model=CrawlerFullConfig)
async def get_default_crawler_config():
    """获取默认爬虫配置"""
    try:
        config = config_service._get_default_config()
        # 不隐藏API密钥，因为这是默认配置
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取默认配置失败: {str(e)}")



def _resolve_api_url(base_url: str) -> list[str]:
    """解析API URL，处理Docker环境中的localhost问题"""
    import os
    from urllib.parse import urlparse, urlunparse

    parsed = urlparse(base_url)

    # 如果不是localhost，直接返回原URL
    if parsed.hostname not in ['localhost', '127.0.0.1']:
        return [base_url]

    # 检测是否在Docker环境中
    in_docker = os.path.exists('/.dockerenv') or os.path.exists('/proc/1/cgroup')

    urls_to_try = [base_url]  # 总是先尝试原始URL

    if in_docker:
        # 在Docker中，尝试host.docker.internal
        docker_url = urlunparse((
            parsed.scheme,
            f"host.docker.internal:{parsed.port}",
            parsed.path,
            parsed.params,
            parsed.query,
            parsed.fragment
        ))
        urls_to_try.append(docker_url)

        # 也尝试**********（Docker默认网关）
        gateway_url = urlunparse((
            parsed.scheme,
            f"**********:{parsed.port}",
            parsed.path,
            parsed.params,
            parsed.query,
            parsed.fragment
        ))
        urls_to_try.append(gateway_url)

    return urls_to_try






