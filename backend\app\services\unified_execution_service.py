"""
统一执行关联服务
管理监控任务、URL、爬虫Worker、Celery任务之间的完整关联关系
"""

import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass
import redis
import redis.asyncio as aioredis
import uuid

logger = logging.getLogger(__name__)


@dataclass
class ExecutionSession:
    """执行会话 - 一次监控任务执行的完整记录"""
    session_id: str
    monitoring_task_id: str
    celery_task_id: str
    execution_type: str  # manual|scheduled
    started_at: str
    status: str  # running|completed|failed
    total_urls: int = 0
    completed_urls: int = 0
    failed_urls: int = 0
    ended_at: Optional[str] = None


@dataclass
class URLExecution:
    """URL执行记录 - 单个URL的执行详情"""
    execution_id: str
    session_id: str
    monitoring_task_id: str
    celery_task_id: str
    url_id: str
    url: str
    assigned_worker_id: str
    crawler_endpoint: str
    started_at: str
    status: str  # pending|running|completed|failed
    completed_at: Optional[str] = None
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    retry_count: int = 0


class UnifiedExecutionService:
    """统一执行关联服务"""
    
    def __init__(self, redis_url: str = "redis://redis:6379/0"):
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.async_redis_client = aioredis.from_url(redis_url, decode_responses=True)
        
        # Redis键模式
        self.keys = {
            # 核心实体
            'execution_session': 'execution_sessions:{}',
            'url_execution': 'url_executions:{}',
            'url_info': 'urls:{}:info',
            
            # 关联关系
            'session_urls': 'execution_sessions:{}:urls',
            'url_tasks': 'urls:{}:tasks',
            'monitoring_task_sessions': 'monitoring_task_sessions:{}',
            'celery_task_sessions': 'celery_task_sessions:{}',
            'worker_url_executions': 'worker_url_executions:{}',
            'url_execution_history': 'url_execution_history:{}',
            
            # 状态索引
            'sessions_by_status': 'execution_sessions:by_status:{}',
            'executions_by_status': 'url_executions:by_status:{}',
            'sessions_by_date': 'execution_sessions:by_date:{}',
            'executions_by_hour': 'url_executions:by_hour:{}',
            
            # 实时状态
            'current_executions_by_worker': 'current_executions:by_worker:{}',
            'current_sessions_active': 'current_sessions:active',
            'worker_load': 'worker_load:{}',
            
            # 辅助索引
            'url_hash_to_id': 'url_hash_to_id:{}',
            'url_to_id': 'url_to_id:{}'
        }
    
    def _get_url_hash(self, url: str) -> str:
        """获取URL的哈希值"""
        return hashlib.md5(url.encode()).hexdigest()[:16]
    
    def _generate_session_id(self) -> str:
        """生成执行会话ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"exec_{timestamp}_{str(uuid.uuid4())[:8]}"
    
    def _generate_url_execution_id(self) -> str:
        """生成URL执行记录ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"url_exec_{timestamp}_{str(uuid.uuid4())[:8]}"
    
    def _generate_url_id(self, url: str) -> str:
        """生成URL ID"""
        url_hash = self._get_url_hash(url)
        return f"url_{url_hash}"
    
    async def create_execution_session(
        self,
        monitoring_task_id: str,
        celery_task_id: str,
        execution_type: str = "manual",
        urls: List[str] = None
    ) -> ExecutionSession:
        """创建执行会话"""
        try:
            session_id = self._generate_session_id()
            now = datetime.now().isoformat()
            
            session = ExecutionSession(
                session_id=session_id,
                monitoring_task_id=monitoring_task_id,
                celery_task_id=celery_task_id,
                execution_type=execution_type,
                started_at=now,
                status="running",
                total_urls=len(urls) if urls else 0
            )
            
            # 存储会话信息
            session_key = self.keys['execution_session'].format(session_id)
            session_data = {
                "session_id": session.session_id,
                "monitoring_task_id": session.monitoring_task_id,
                "celery_task_id": session.celery_task_id,
                "execution_type": session.execution_type,
                "started_at": session.started_at,
                "status": session.status,
                "total_urls": str(session.total_urls),
                "completed_urls": str(session.completed_urls),
                "failed_urls": str(session.failed_urls)
            }
            
            pipe = self.redis_client.pipeline()
            
            # 1. 存储会话数据
            pipe.hset(session_key, mapping=session_data)
            
            # 2. 更新关联索引
            pipe.sadd(self.keys['monitoring_task_sessions'].format(monitoring_task_id), session_id)
            pipe.sadd(self.keys['celery_task_sessions'].format(celery_task_id), session_id)
            
            # 3. 更新状态索引
            pipe.sadd(self.keys['sessions_by_status'].format("running"), session_id)
            pipe.sadd(self.keys['current_sessions_active'], session_id)
            
            # 4. 更新日期索引
            date_key = datetime.now().strftime("%Y-%m-%d")
            pipe.sadd(self.keys['sessions_by_date'].format(date_key), session_id)
            
            # 5. 设置过期时间（90天）
            pipe.expire(session_key, 90 * 24 * 3600)
            
            pipe.execute()
            
            logger.info(f"Created execution session: {session_id}")
            return session
            
        except Exception as e:
            logger.error(f"Failed to create execution session: {e}")
            raise
    
    async def create_url_execution(
        self,
        session_id: str,
        monitoring_task_id: str,
        celery_task_id: str,
        url: str,
        assigned_worker_id: str,
        crawler_endpoint: str
    ) -> URLExecution:
        """创建URL执行记录"""
        try:
            execution_id = self._generate_url_execution_id()
            url_id = self._generate_url_id(url)
            url_hash = self._get_url_hash(url)
            now = datetime.now().isoformat()
            
            # 确保URL信息存在
            self._ensure_url_info(url_id, url, url_hash)
            
            execution = URLExecution(
                execution_id=execution_id,
                session_id=session_id,
                monitoring_task_id=monitoring_task_id,
                celery_task_id=celery_task_id,
                url_id=url_id,
                url=url,
                assigned_worker_id=assigned_worker_id,
                crawler_endpoint=crawler_endpoint,
                started_at=now,
                status="pending"
            )
            
            # 存储执行记录
            execution_key = self.keys['url_execution'].format(execution_id)
            execution_data = {
                "execution_id": execution.execution_id,
                "session_id": execution.session_id,
                "monitoring_task_id": execution.monitoring_task_id,
                "celery_task_id": execution.celery_task_id,
                "url_id": execution.url_id,
                "url": execution.url,
                "assigned_worker_id": execution.assigned_worker_id,
                "crawler_endpoint": execution.crawler_endpoint,
                "started_at": execution.started_at,
                "status": execution.status,
                "retry_count": str(execution.retry_count)
            }
            
            pipe = self.redis_client.pipeline()
            
            # 1. 存储执行数据
            pipe.hset(execution_key, mapping=execution_data)
            
            # 2. 更新关联索引
            pipe.sadd(self.keys['session_urls'].format(session_id), execution_id)
            pipe.sadd(self.keys['worker_url_executions'].format(assigned_worker_id), execution_id)
            pipe.sadd(self.keys['url_execution_history'].format(url_id), execution_id)
            
            # 3. 更新状态索引
            pipe.sadd(self.keys['executions_by_status'].format("pending"), execution_id)
            
            # 4. 更新时间索引
            hour_key = datetime.now().strftime("%Y-%m-%d-%H")
            pipe.sadd(self.keys['executions_by_hour'].format(hour_key), execution_id)
            
            # 5. 更新实时状态
            pipe.hset(self.keys['current_executions_by_worker'].format(assigned_worker_id), execution_id, url)
            
            # 6. 设置过期时间（30天）
            pipe.expire(execution_key, 30 * 24 * 3600)
            
            pipe.execute()
            
            logger.info(f"Created URL execution: {execution_id} for {url}")
            return execution
            
        except Exception as e:
            logger.error(f"Failed to create URL execution for {url}: {e}")
            raise
    
    def _ensure_url_info(self, url_id: str, url: str, url_hash: str):
        """确保URL信息存在"""
        try:
            url_info_key = self.keys['url_info'].format(url_id)
            
            # 检查是否已存在
            if self.redis_client.exists(url_info_key):
                return
            
            # 创建URL信息
            now = datetime.now().isoformat()
            url_info = {
                "id": url_id,
                "url": url,
                "url_hash": url_hash,
                "platform": self._detect_platform(url),
                "status": "active",
                "created_at": now,
                "last_crawled": ""
            }
            
            pipe = self.redis_client.pipeline()
            
            # 存储URL信息
            pipe.hset(url_info_key, mapping=url_info)
            
            # 建立哈希索引
            pipe.set(self.keys['url_hash_to_id'].format(url_hash), url_id)
            pipe.set(self.keys['url_to_id'].format(url), url_id)
            
            pipe.execute()
            
        except Exception as e:
            logger.error(f"Failed to ensure URL info for {url}: {e}")
            raise
    
    def _detect_platform(self, url: str) -> str:
        """检测URL平台"""
        if "mercadolibre" in url:
            return "mercadolibre"
        elif "amazon" in url:
            return "amazon"
        elif "ebay" in url:
            return "ebay"
        else:
            return "unknown"
    
    async def update_url_execution_status(
        self,
        execution_id: str,
        status: str,
        response_time: Optional[float] = None,
        error_message: Optional[str] = None
    ):
        """更新URL执行状态"""
        try:
            execution_key = self.keys['url_execution'].format(execution_id)
            
            # 获取当前状态
            current_data = self.redis_client.hgetall(execution_key)
            if not current_data:
                logger.warning(f"URL execution {execution_id} not found")
                return
            
            old_status = current_data.get("status", "unknown")
            
            pipe = self.redis_client.pipeline()
            
            # 更新执行记录
            update_data = {"status": status}
            if status in ["completed", "failed"]:
                update_data["completed_at"] = datetime.now().isoformat()
            if response_time is not None:
                update_data["response_time"] = str(response_time)
            if error_message:
                update_data["error_message"] = error_message
            
            pipe.hset(execution_key, mapping=update_data)
            
            # 更新状态索引
            if old_status != status:
                pipe.srem(self.keys['executions_by_status'].format(old_status), execution_id)
                pipe.sadd(self.keys['executions_by_status'].format(status), execution_id)
            
            # 如果完成，从实时状态中移除
            if status in ["completed", "failed"]:
                worker_id = current_data.get("assigned_worker_id")
                if worker_id:
                    pipe.hdel(self.keys['current_executions_by_worker'].format(worker_id), execution_id)
            
            pipe.execute()
            
            logger.info(f"Updated URL execution {execution_id} status: {old_status} -> {status}")
            
        except Exception as e:
            logger.error(f"Failed to update URL execution status {execution_id}: {e}")
            raise


    async def get_current_url_execution_status(self, url: str) -> Optional[Dict[str, Any]]:
        """查询URL当前执行状态"""
        try:
            # 1. 通过URL获取url_id
            url_id_key = self.keys['url_to_id'].format(url)
            url_id = await self.async_redis_client.get(url_id_key)

            if not url_id:
                return None

            # 2. 获取URL执行历史
            execution_ids = await self.async_redis_client.smembers(self.keys['url_execution_history'].format(url_id))

            # 3. 查找最新的执行记录（包括已完成的）
            latest_execution = None
            latest_time = ""

            for execution_id in execution_ids:
                execution_data = await self.async_redis_client.hgetall(self.keys['url_execution'].format(execution_id))
                if execution_data:
                    started_at = execution_data.get("started_at", "")
                    if started_at > latest_time:
                        latest_time = started_at
                        latest_execution = {
                            "url": url,
                            "execution_id": execution_id,
                            "monitoring_task_id": execution_data.get("monitoring_task_id"),
                            "celery_task_id": execution_data.get("celery_task_id"),
                            "assigned_worker_id": execution_data.get("assigned_worker_id"),
                            "crawler_endpoint": execution_data.get("crawler_endpoint"),
                            "status": execution_data.get("status"),
                            "started_at": execution_data.get("started_at"),
                            "completed_at": execution_data.get("completed_at"),
                            "response_time": execution_data.get("response_time"),
                            "error_message": execution_data.get("error_message")
                        }

            return latest_execution

        except Exception as e:
            logger.error(f"Failed to get current URL execution status for {url}: {e}")
            return None

    async def get_monitoring_task_execution_chain(self, task_uuid: str) -> Dict[str, Any]:
        """获取监控任务完整执行链路"""
        try:
            # 1. 获取所有执行会话
            session_ids = await self.async_redis_client.smembers(self.keys['monitoring_task_sessions'].format(task_uuid))

            execution_chain = []
            for session_id in session_ids:
                # 2. 获取会话信息
                session_data = await self.async_redis_client.hgetall(self.keys['execution_session'].format(session_id))

                if not session_data:
                    continue

                # 3. 获取会话中的URL执行记录
                url_execution_ids = await self.async_redis_client.smembers(self.keys['session_urls'].format(session_id))

                url_executions = []
                for url_execution_id in url_execution_ids:
                    url_execution = await self.async_redis_client.hgetall(self.keys['url_execution'].format(url_execution_id))
                    if url_execution:
                        url_executions.append(url_execution)

                execution_chain.append({
                    "session": session_data,
                    "url_executions": url_executions
                })

            return {
                "monitoring_task_id": task_uuid,
                "total_sessions": len(execution_chain),
                "execution_chain": execution_chain
            }

        except Exception as e:
            logger.error(f"Failed to get monitoring task execution chain for {task_uuid}: {e}")
            return {"monitoring_task_id": task_uuid, "total_sessions": 0, "execution_chain": []}

    async def get_worker_current_load(self, worker_id: str) -> Dict[str, Any]:
        """获取Worker当前负载"""
        try:
            # 获取当前执行的URL
            current_executions = await self.async_redis_client.hgetall(self.keys['current_executions_by_worker'].format(worker_id))

            # 获取负载信息
            load_data = await self.async_redis_client.hgetall(self.keys['worker_load'].format(worker_id))

            return {
                "worker_id": worker_id,
                "current_tasks": len(current_executions),
                "current_urls": list(current_executions.values()),
                "max_tasks": int(load_data.get("max_tasks", 10)),
                "last_updated": load_data.get("last_updated", "")
            }

        except Exception as e:
            logger.error(f"Failed to get worker current load for {worker_id}: {e}")
            return {"worker_id": worker_id, "current_tasks": 0, "current_urls": [], "max_tasks": 10}

    async def get_celery_task_execution_info(self, celery_task_id: str) -> Dict[str, Any]:
        """通过Celery任务ID获取执行信息"""
        try:
            # 获取关联的执行会话
            session_ids = await self.async_redis_client.smembers(self.keys['celery_task_sessions'].format(celery_task_id))

            if not session_ids:
                return {"celery_task_id": celery_task_id, "sessions": []}

            sessions = []
            for session_id in session_ids:
                session_data = await self.async_redis_client.hgetall(self.keys['execution_session'].format(session_id))
                if session_data:
                    sessions.append(session_data)

            return {
                "celery_task_id": celery_task_id,
                "total_sessions": len(sessions),
                "sessions": sessions
            }

        except Exception as e:
            logger.error(f"Failed to get Celery task execution info for {celery_task_id}: {e}")
            return {"celery_task_id": celery_task_id, "sessions": []}

    async def cleanup_expired_data(self, days: int = 30):
        """清理过期数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_timestamp = cutoff_date.isoformat()

            # 清理过期的URL执行记录
            expired_count = 0

            # 获取所有状态的执行记录
            for status in ["completed", "failed"]:
                execution_ids = self.redis_client.smembers(self.keys['executions_by_status'].format(status))

                for execution_id in execution_ids:
                    execution_data = self.redis_client.hgetall(self.keys['url_execution'].format(execution_id))

                    if execution_data and execution_data.get("completed_at", "") < cutoff_timestamp:
                        # 删除过期记录
                        self._delete_url_execution(execution_id, execution_data)
                        expired_count += 1

            logger.info(f"Cleaned up {expired_count} expired URL execution records")
            return expired_count

        except Exception as e:
            logger.error(f"Failed to cleanup expired data: {e}")
            return 0

    def _delete_url_execution(self, execution_id: str, execution_data: Dict[str, str]):
        """删除URL执行记录及其索引"""
        try:
            pipe = self.redis_client.pipeline()

            # 删除主记录
            pipe.delete(self.keys['url_execution'].format(execution_id))

            # 删除索引
            session_id = execution_data.get("session_id")
            worker_id = execution_data.get("assigned_worker_id")
            url_id = execution_data.get("url_id")
            status = execution_data.get("status")

            if session_id:
                pipe.srem(self.keys['session_urls'].format(session_id), execution_id)
            if worker_id:
                pipe.srem(self.keys['worker_url_executions'].format(worker_id), execution_id)
            if url_id:
                pipe.srem(self.keys['url_execution_history'].format(url_id), execution_id)
            if status:
                pipe.srem(self.keys['executions_by_status'].format(status), execution_id)

            pipe.execute()

        except Exception as e:
            logger.error(f"Failed to delete URL execution {execution_id}: {e}")


# 全局服务实例
unified_execution_service = UnifiedExecutionService()
